import { Outlet } from 'react-router-dom';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ooter, 
  SidebarHeader, 
  SidebarInset, 
  SidebarProvider, 
  SidebarRail, 
  SidebarTrigger 
} from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { ThemeToggle } from '@/components/ThemeToggle';
import logoImage from "@/assets/logo.png";
import { NavUser } from "@/layouts/components/nav-user.tsx";
import { DynamicNavigation } from '@/components/navigation/DynamicNavigation';
import { usePermissions } from '@/contexts/PermissionContext';
import { AlertCircle } from 'lucide-react';
import { Toaster } from "@/components/ui/sonner"
export default function RootLayout() {
  const { error, isLoading } = usePermissions();

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <Sidebar collapsible="icon">
          <SidebarHeader>
            <div className="flex items-center gap-3 group-data-[collapsible=icon]:justify-center">
              <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-white border border-gray-200 shadow-sm">
                <img
                  src={logoImage}
                  alt="语文出版社logo"
                  className="w-8 h-8 object-contain"
                />
              </div>
              <div className="flex flex-col group-data-[collapsible=icon]:hidden">
                <h1 className="text-lg font-bold text-sidebar-foreground">Teach Wise</h1>
                <p className="text-xs text-sidebar-foreground/70">语文出版社数智辅教综合服务平台</p>
              </div>
            </div>
          </SidebarHeader>

          <SidebarContent>
            {/* 权限错误提示 */}
            {error && (
              <div className="p-4">
                <div className="relative w-full rounded-lg border border-destructive/50 bg-destructive/5 p-4 text-destructive">
                  <AlertCircle className="h-4 w-4 absolute left-4 top-4" />
                  <div className="pl-7 text-sm">
                    权限加载失败，请刷新页面重试
                  </div>
                </div>
              </div>
            )}

            {/* 动态导航菜单 */}
            <DynamicNavigation
              showLoading={true}
              showError={true}
              groupByCategory={true}
              defaultExpanded={true}
            />
          </SidebarContent>

          <SidebarFooter>
            <NavUser user={{ avatar: 'https://avatars.githubusercontent.com/u/10656201?v=4' }} />
          </SidebarFooter>
          
          <SidebarRail />
        </Sidebar>

        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-6">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <div className="flex items-center justify-between flex-1">
              <div className="flex items-center space-x-2">
                <h2 className="text-lg font-semibold">工作台</h2>
                {isLoading && (
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <div className="animate-spin h-3 w-3 border border-primary border-t-transparent rounded-full"></div>
                    加载权限中...
                  </div>
                )}
              </div>
              <ThemeToggle />
            </div>
          </header>
          
          <main className="flex-1 overflow-auto p-6">
            <div className="max-w-7xl mx-auto">
              <Outlet />
            </div>
          </main>
          <Toaster />
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}