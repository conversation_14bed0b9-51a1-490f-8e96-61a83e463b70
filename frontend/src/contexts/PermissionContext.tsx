import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { permissionApi } from '@/services/permissionApi';

// 权限类型定义
export interface Permission {
  resource: string;
  action: string;
  scope?: string;
}

// 菜单权限结构
export interface MenuPermission {
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type: string;
  required_roles?: string[];
  required_permissions?: string[];
  sort_order?: number;
  children?: MenuPermission[];
}

// 数据范围权限
export interface DataScope {
  resource: string;
  scope_type: string;
  scope_value: string;
  actions: string[];
}

// 用户角色信息
export interface UserRole {
  role_id: string;
  role_code: string;
  role_name: string;
  level: number;
  category: string;
}

// 权限上下文类型
interface PermissionContextType {
  // 权限状态
  permissions: Permission[];
  menus: MenuPermission[];
  dataScopes: DataScope[];
  roles: UserRole[];
  isLoading: boolean;
  error: string | null;

  // 权限检查方法
  hasPermission: (resource: string, action: string, scope?: string) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;

  getAccessibleMenus: () => MenuPermission[];

  // 角色检查
  hasRole: (roleCode: string) => boolean;
  hasAnyRole: (roleCodes: string[]) => boolean;
  isSystemAdmin: () => boolean;

  // 数据权限检查
  getDataScopes: (resource: string) => DataScope[];
  canAccessData: (resource: string, scopeType: string, scopeValue: string) => boolean;

  // 刷新权限
  refreshPermissions: () => Promise<void>;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

export const PermissionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated, identity, token } = useAuth();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [menus, setMenus] = useState<MenuPermission[]>([]);
  const [dataScopes, setDataScopes] = useState<DataScope[]>([]);
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载用户权限数据
  const loadPermissions = async () => {
    console.log("🔄 开始加载权限数据", { isAuthenticated, hasIdentity: !!identity, hasToken: !!token });
    
    if (!isAuthenticated  || !token) {
      console.log("⚠️ 缺少认证条件，清空权限数据", { isAuthenticated, hasIdentity: !!identity, hasToken: !!token });
      // 清空权限数据
      setPermissions([]);
      setMenus([]);
      setDataScopes([]);
      setRoles([]);
      return;
    }

    // console.log("📋 开始并行加载权限数据，tenant_id:", identity.tenant_id);
    setIsLoading(true);
    setError(null);

    try {
      // 并行加载权限数据
      console.log("🌐 调用API: getUserMenus 和 getUserRoles");
      const [menusResponse, rolesResponse] = await Promise.all([
        permissionApi.getUserMenus(identity?.tenant_id || '7ff2e111-1ca4-4402-bc02-af69c1a7283c'),
        permissionApi.getUserRoles(identity?.tenant_id || '7ff2e111-1ca4-4402-bc02-af69c1a7283c'),
      ]);
      console.log("✅ API调用成功");
      console.log("📋 菜单响应:", menusResponse);
      console.log("👥 角色响应:", rolesResponse);
      
      setMenus(menusResponse.menus || []);
      setRoles(rolesResponse.roles || []);

      // 从菜单中提取权限
      const extractedPermissions = extractPermissionsFromMenus(menusResponse.menus || []);
      console.log("🔑 提取到的权限:", extractedPermissions);
      setPermissions(extractedPermissions);

      // 加载数据权限范围
      if (extractedPermissions.length > 0) {
        const uniqueResources = [...new Set(extractedPermissions.map(p => p.resource))];
        console.log("🎯 需要加载数据权限的资源:", uniqueResources);
        const dataScopePromises = uniqueResources.map(resource =>
          permissionApi.getUserDataScopes(identity?.tenant_id||'', resource)
        );

        const dataScopeResults = await Promise.all(dataScopePromises);
        const allDataScopes = dataScopeResults.flatMap(result => result.data_scopes || []);
        console.log("📊 数据权限范围:", allDataScopes);
        setDataScopes(allDataScopes);
      }

      console.log("🎉 权限数据加载完成");
    } catch (err) {
      console.error('❌ 权限数据加载失败:', err);
      setError(err instanceof Error ? err.message : 'Failed to load permissions');
    } finally {
      setIsLoading(false);
    }
  };

  // 从菜单中提取权限列表
  // 注意：后端已经验证过权限，这里主要用于兼容性，安全处理可能缺失的权限字段
  const extractPermissionsFromMenus = (menus: MenuPermission[]): Permission[] => {
    const permissions: Permission[] = [];

    const extractFromMenu = (menu: MenuPermission) => {
      // 安全检查：只有当 required_permissions 存在且为数组时才处理
      if (menu.required_permissions && Array.isArray(menu.required_permissions)) {
        menu.required_permissions.forEach(perm => {
          const [resource, action, scope] = perm.split(':');
          if (resource && action) {
            permissions.push({
              resource,
              action,
              scope: scope || undefined
            });
          }
        });
      }

      if (menu.children) {
        menu.children.forEach(extractFromMenu);
      }
    };

    menus.forEach(extractFromMenu);
    return permissions;
  };

  // 检查单个权限
  const hasPermission = (resource: string, action: string, scope?: string): boolean => {
    // 系统管理员拥有所有权限
    if (isSystemAdmin()) {
      return true;
    }

    return permissions.some(permission => {
      const resourceMatch = permission.resource === resource || permission.resource === '*';
      const actionMatch = permission.action === action || permission.action === '*';
      const scopeMatch = !scope || !permission.scope || 
                        permission.scope === scope || permission.scope === '*';
      
      return resourceMatch && actionMatch && scopeMatch;
    });
  };

  // 检查是否拥有任意一个权限
  const hasAnyPermission = (perms: Permission[]): boolean => {
    return perms.some(perm => hasPermission(perm.resource, perm.action, perm.scope));
  };

  // 检查是否拥有所有权限
  const hasAllPermissions = (perms: Permission[]): boolean => {
    return perms.every(perm => hasPermission(perm.resource, perm.action, perm.scope));
  };

  // 获取可访问的菜单列表
  const getAccessibleMenus = (): MenuPermission[] => {
    const filterMenus = (menuList: MenuPermission[]): MenuPermission[] => {
      return menuList.filter(menu => {
        if (menu.children) {
          menu.children = filterMenus(menu.children);
        }
        return true;
      });
    };

    return filterMenus([...menus]);
  };

  // 检查角色
  const hasRole = (roleCode: string): boolean => {
    return roles.some(role => role.role_code === roleCode);
  };

  // 检查是否拥有任意角色
  const hasAnyRole = (roleCodes: string[]): boolean => {
    return roleCodes.some(roleCode => hasRole(roleCode));
  };

  // 检查是否为系统管理员
  const isSystemAdmin = (): boolean => {
    return roles.some(role => 
      role.role_code.includes('super_admin') || 
      role.role_code.includes('system_admin') ||
      role.level === 1
    );
  };

  // 获取资源的数据权限范围
  const getDataScopes = (resource: string): DataScope[] => {
    return dataScopes.filter(scope => scope.resource === resource);
  };

  // 检查数据访问权限
  const canAccessData = (resource: string, scopeType: string, scopeValue: string): boolean => {
    // 系统管理员拥有所有数据访问权限
    if (isSystemAdmin()) {
      return true;
    }

    const resourceScopes = getDataScopes(resource);
    return resourceScopes.some(scope => 
      scope.scope_type === scopeType && 
      (scope.scope_value === scopeValue || scope.scope_value === '*')
    );
  };

  // 刷新权限数据
  const refreshPermissions = async (): Promise<void> => {
    await loadPermissions();
  };

  // 当认证状态改变时重新加载权限
  useEffect(() => {
    console.log("🔄 认证状态变化，触发权限加载检查", { isAuthenticated, hasIdentity: !!identity });
    
    if (isAuthenticated) {
      console.log("✅ 认证条件满足，开始加载权限");
      loadPermissions();
    } else {
      console.log("⚠️ 认证条件不满足，清空权限数据");
      // 清空权限数据
      setPermissions([]);
      setMenus([]);
      setDataScopes([]);
      setRoles([]);
      setError(null);
    }
  }, [isAuthenticated, identity]);

  const contextValue: PermissionContextType = {
    permissions,
    menus,
    dataScopes,
    roles,
    isLoading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getAccessibleMenus,
    hasRole,
    hasAnyRole,
    isSystemAdmin,
    getDataScopes,
    canAccessData,
    refreshPermissions,
  };

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  );
};