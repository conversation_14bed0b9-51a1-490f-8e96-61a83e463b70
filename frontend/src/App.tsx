import { RouterProvider } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { MenuProvider } from './contexts/MenuContext';
import { PermissionProvider } from './contexts/PermissionContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { routerWithPermissions } from './router/indexWithPermissions';
import { Toaster } from "sonner";

function App() {
    return (
        <ThemeProvider>
            <AuthProvider>
                <MenuProvider>
                    <PermissionProvider>
                        <RouterProvider router={routerWithPermissions} />
                        <Toaster position="bottom-right" />
                    </PermissionProvider>
                </MenuProvider>
            </AuthProvider>
        </ThemeProvider>
    );
}

export default App;
